---
type: "always_apply"
---

# 通用开发准则

    •	使用 MARK: 分区以提升代码可读性。
    •	避免视图中出现业务逻辑代码。
    •	所有颜色和字体样式通过统一命名管理。
    •	不要硬编码字符串、图片名或颜色。
    •	所有功能组件应支持预览（PreviewProvider）。
    •	避免视图直接访问服务或模型层。
    •	状态应只由 ViewModel 控制。
    •	保持文件命名与功能一致，清晰可搜索。

https://www.carboncoin.asia/api/是后端的公网ip

我已经为颜色的创建进行了拓展：直接使用 hex 创建复合颜色，比如：**Color**(**hex**: "4B7905"**)**，避免冗杂的定义。

全局颜色样式参考文件 ColorExtensions.swift 以及 assets

对于复杂的定位，使用几何计算（`GeometryReader`）或固定比例偏移，避免使用硬编码，确保在各个平台上比例合适

不要尝试修改作者，比如 Created by Augment Agent on 2025-08-17.是禁止的！

不要认为和尝试修改：

1. ColorExtensions.swift 中引用了不存在的 auxiliaryYellow 和 skyBlue 颜 色

他妈的我的设置根本没问题！

# 项目架构与文件组织

请参考下面的文件夹结构管理文件、实践 MVVM 开发思想:

## Models/

    •	所有模型应遵循 Codable 和 Identifiable 协议。
    •	模型结构应紧贴接口返回结构。
    •	使用简洁字段名与后端字段保持一致。
    •	保持模型纯净，不包含业务逻辑。
    •	若存在转换逻辑，应在专用转换器中处理。

## ViewModels/

    •	ViewModel 应仅负责状态与业务逻辑处理。
    •	使用 @Published 管理可观察状态。
    •	初始化时注入服务层接口，避免直接调用实现类。
    •	异步任务使用 async/await 统一处理。
    •	错误信息与加载状态应有独立状态字段。
    •	ViewModel 文件命名应为 [Feature]ViewModel.swift。

## Services/

    •	每个服务应定义协议并提供实现类。
    •	所有网络请求（如使用 URLSession）应统一封装到服务中，ViewModel 不应直接处理网络请求。
    •	不直接在 ViewModel 中处理 URLSession。
    •	接口路径、参数、响应应抽象为结构体。
    •	提供默认实现与 Mock 实现以便测试。
    •	不直接使用第三方 API，应通过适配封装。

## Protocols/

    •	所有协议命名应以 Protocol 或 Delegate 结尾。
    •	保持接口粒度小、职责单一。
    •	协议需配合默认实现使用扩展定义。
    •	不允许冗余定义未使用的协议方法。
    •	用于约束服务、组件、状态行为的抽象。

## Views/

    •	所有 View 应为纯展示组件。
    •	UI 控件绑定状态由 ViewModel 提供。
    •	每个主页面应拆分为多个子组件。
    •	命名采用 [Feature]View.swift 统一规范。
    •	尽量避免 .onAppear 做过多逻辑。
    •	支持浅色、深色模式与动态字体。

## Resources/

    •	所有颜色应在 Asset 中定义并语义命名。
    •	图片资源统一前缀命名如 icon_、bg_。
    •	避免直接使用字符串调用资源。
    •	字体、图标、颜色统一封装为静态访问。
    •	所有资源应支持 Dark Mode 适配。

## Styles/

    •	颜色命名使用语义词如 primary, secondary。
    •	字体样式统一封装为静态常量。
    •	所有间距使用统一命名常量，如 spacingM。
    •	避免在组件中直接设置 size 或 color 值。
    •	样式类文件命名为 Theme.swift 或 StyleGuide.swift。

## Utilities/

    •	工具函数必须具备明确单一功能。
    •	所有扩展分类应按类型命名如 Date+Format.swift。
    •	避免 ViewModel 中定义工具方法。
    •	错误处理、数据格式化集中封装。
    •	公共常量统一收纳于 Constants 文件中。
