//
//  ContentView.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/8/15.
//

import SwiftUI

struct ContentView: View {
    @EnvironmentObject private var healthManager: HealthManager
    @StateObject private var healthDataViewModel : HealthDataViewModel
    @StateObject private var authManager = AuthManager()
    @AppStorage("isLoggedIn") private var isLoggedIn = false

    init(){
        // 使用未初始化的 healthManager 会报错，所以需要使用 `_healthDataViewModel`
        let healthManager = HealthManager() // 或其他方式获取
        self._healthDataViewModel = StateObject(wrappedValue: HealthDataViewModel(dataType: .steps, healthManager: healthManager))
    }

    var body: some View {
        Group {
            if isLoggedIn {
                // 已登录，显示主界面
                MainTabView()
                    .environmentObject(healthDataViewModel)
                    .environmentObject(authManager)
            } else {
                // 未登录，显示登录界面
                LoginView()
                    .environmentObject(authManager)
            }
        }
        .onAppear {
            // 检查登录状态
            checkAuthState()
        }
        .onChange(of: authManager.isLoggedIn) { _, newValue in
            isLoggedIn = newValue
        }
    }

    // MARK: - Private Methods

    /// 检查认证状态
    private func checkAuthState() {
        // 同步AuthManager的登录状态
        isLoggedIn = authManager.isLoggedIn
    }
}

#Preview {
    ContentView()
}
