//
//  LocationModels.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/8/30.
//

import Foundation
import CoreLocation

// MARK: - 用户位置信息模型

/// 用户位置信息
struct UserLocationInfo: Codable, Identifiable, Equatable {
    let id = UUID()
    let userId: String
    let latitude: Double
    let longitude: Double
    let timestamp: Date
    let accuracy: Double?
    let locationString: String?
    
    enum CodingKeys: String, CodingKey {
        case userId, latitude, longitude, timestamp, accuracy, locationString
    }
    
    /// 从CLLocation创建位置信息
    init(userId: String, location: CLLocation, locationString: String? = nil) {
        self.userId = userId
        self.latitude = location.coordinate.latitude
        self.longitude = location.coordinate.longitude
        self.timestamp = location.timestamp
        self.accuracy = location.horizontalAccuracy
        self.locationString = locationString
    }
    
    /// 手动创建位置信息
    init(userId: String, latitude: Double, longitude: Double, timestamp: Date = Date(), accuracy: Double? = nil, locationString: String? = nil) {
        self.userId = userId
        self.latitude = latitude
        self.longitude = longitude
        self.timestamp = timestamp
        self.accuracy = accuracy
        self.locationString = locationString
    }
    
    /// 转换为CLLocation
    var clLocation: CLLocation {
        return CLLocation(
            coordinate: CLLocationCoordinate2D(latitude: latitude, longitude: longitude),
            altitude: 0,
            horizontalAccuracy: accuracy ?? 0,
            verticalAccuracy: 0,
            timestamp: timestamp
        )
    }
    
    /// 格式化的时间戳
    var formattedTimestamp: String {
        let formatter = DateFormatter()
        formatter.dateStyle = .short
        formatter.timeStyle = .short
        return formatter.string(from: timestamp)
    }
}

// MARK: - API请求模型

/// 更新用户位置请求
struct UpdateLocationRequest: Codable {
    let userId: String
    let latitude: Double
    let longitude: Double
    let timestamp: String
    let accuracy: Double?
    
    init(userId: String, location: CLLocation) {
        self.userId = userId
        self.latitude = location.coordinate.latitude
        self.longitude = location.coordinate.longitude
        self.timestamp = ISO8601DateFormatter().string(from: location.timestamp)
        self.accuracy = location.horizontalAccuracy
    }
    
    init(userLocationInfo: UserLocationInfo) {
        self.userId = userLocationInfo.userId
        self.latitude = userLocationInfo.latitude
        self.longitude = userLocationInfo.longitude
        self.timestamp = ISO8601DateFormatter().string(from: userLocationInfo.timestamp)
        self.accuracy = userLocationInfo.accuracy
    }
}

/// 查询用户位置请求
struct QueryLocationRequest: Codable {
    let userId: String
}

// MARK: - API响应模型

/// 位置操作响应
struct LocationResponse: Codable {
    let success: Bool
    let message: String?
    let data: LocationData?
    let error: String?
}

/// 位置数据
struct LocationData: Codable {
    let userId: String
    let latitude: Double
    let longitude: Double
    let timestamp: Date
    let lastUpdate: Date
    
    enum CodingKeys: String, CodingKey {
        case userId, latitude, longitude, timestamp, lastUpdate
    }
    
    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        
        userId = try container.decode(String.self, forKey: .userId)
        latitude = try container.decode(Double.self, forKey: .latitude)
        longitude = try container.decode(Double.self, forKey: .longitude)
        
        // 处理日期字符串
        let timestampString = try container.decode(String.self, forKey: .timestamp)
        let lastUpdateString = try container.decode(String.self, forKey: .lastUpdate)
        
        let formatter = ISO8601DateFormatter()
        timestamp = formatter.date(from: timestampString) ?? Date()
        lastUpdate = formatter.date(from: lastUpdateString) ?? Date()
    }
    
    /// 转换为UserLocationInfo
    func toUserLocationInfo() -> UserLocationInfo {
        return UserLocationInfo(
            userId: userId,
            latitude: latitude,
            longitude: longitude,
            timestamp: timestamp,
            accuracy: nil,
            locationString: nil
        )
    }
}

// MARK: - 位置历史记录模型

/// 位置历史记录（本地存储）
struct LocationHistoryItem: Codable, Identifiable, Equatable {
    let id = UUID()
    let userLocationInfo: UserLocationInfo
    let createdAt: Date
    
    enum CodingKeys: String, CodingKey {
        case userLocationInfo, createdAt
    }
    
    init(userLocationInfo: UserLocationInfo, createdAt: Date = Date()) {
        self.userLocationInfo = userLocationInfo
        self.createdAt = createdAt
    }
    
    /// 格式化的创建时间
    var formattedCreatedAt: String {
        let formatter = RelativeDateTimeFormatter()
        formatter.unitsStyle = .abbreviated
        return formatter.localizedString(for: createdAt, relativeTo: Date())
    }
}

// MARK: - 位置错误类型

/// 位置服务错误枚举
enum LocationError: Error, LocalizedError {
    case permissionDenied
    case locationUnavailable
    case networkError
    case serverError
    case invalidResponse
    case userNotFound
    case invalidLocation
    case unknown(String)
    
    var errorDescription: String? {
        switch self {
        case .permissionDenied:
            return "位置权限被拒绝，请在设置中开启位置权限"
        case .locationUnavailable:
            return "无法获取位置信息"
        case .networkError:
            return "网络连接失败，请检查网络设置"
        case .serverError:
            return "服务器错误，请稍后重试"
        case .invalidResponse:
            return "服务器响应格式错误"
        case .userNotFound:
            return "用户不存在"
        case .invalidLocation:
            return "位置信息无效"
        case .unknown(let message):
            return message
        }
    }
}

// MARK: - 位置更新配置

/// 位置更新配置
struct LocationUpdateConfig {
    /// 更新间隔（秒）
    static let updateInterval: TimeInterval = 5.0
    
    /// 最大历史记录数量
    static let maxHistoryCount: Int = 10
    
    /// 位置精度阈值（米）
    static let accuracyThreshold: Double = 100.0
    
    /// 最小移动距离（米）
    static let minimumDistance: Double = 10.0
    
    /// 请求超时时间（秒）
    static let requestTimeout: TimeInterval = 30.0
}
