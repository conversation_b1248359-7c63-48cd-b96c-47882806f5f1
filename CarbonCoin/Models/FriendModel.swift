//
//  FriendModel.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/8/28.
//

import Foundation

// MARK: - 用户基本信息模型

/// 用户基本信息
struct UserInfo: Codable, Identifiable, Equatable {
    let id = UUID()
    let userId: String
    let nickname: String
    let avatar: String?
    let avatarURL: String?

    enum CodingKeys: String, CodingKey {
        case userId, nickname, avatar, avatarURL
    }
}

// MARK: - 好友关系状态

/// 好友关系状态枚举
enum FriendshipStatus: String, Codable, CaseIterable {
    case pending = "pending"     // 待确认
    case accepted = "accepted"   // 已接受
    case rejected = "rejected"   // 已拒绝

    /// 状态的中文描述
    var localizedDescription: String {
        switch self {
        case .pending:
            return "待确认"
        case .accepted:
            return "已是好友"
        case .rejected:
            return "已拒绝"
        }
    }
}

/// 好友请求类型
enum FriendshipType: String, Codable {
    case sent = "sent"           // 我发送的请求
    case received = "received"   // 我收到的请求

    /// 类型的中文描述
    var localizedDescription: String {
        switch self {
        case .sent:
            return "已发送"
        case .received:
            return "已收到"
        }
    }
}

// MARK: - 好友关系模型

/// 好友关系数据模型
struct Friendship: Codable, Identifiable, Equatable {
    let id = UUID()
    let friendshipId: String
    let friend: UserInfo
    let status: FriendshipStatus
    let type: FriendshipType
    let createdAt: Date
    let updatedAt: Date

    enum CodingKeys: String, CodingKey {
        case friendshipId, friend, status, type, createdAt, updatedAt
    }

    /// 是否可以发送消息（已接受的好友关系）
    var canSendMessage: Bool {
        return status == .accepted
    }

    /// 是否可以接受请求（收到的待确认请求）
    var canAccept: Bool {
        return status == .pending && type == .received
    }

    /// 是否可以拒绝请求（收到的待确认请求）
    var canReject: Bool {
        return status == .pending && type == .received
    }
}

// MARK: - API 请求模型

/// 用户搜索请求
struct UserSearchRequest: Codable {
    let userId: String
}

/// 发送好友请求
struct SendFriendRequestRequest: Codable {
    let userId: String
    let friendId: String
}

/// 处理好友请求
struct HandleFriendRequestRequest: Codable {
    let userId: String
    let friendId: String
    let action: FriendAction

    enum FriendAction: String, Codable {
        case accept = "accept"
        case reject = "reject"

        var localizedDescription: String {
            switch self {
            case .accept:
                return "接受"
            case .reject:
                return "拒绝"
            }
        }
    }
}

/// 删除好友请求
struct DeleteFriendRequest: Codable {
    let userId: String
    let friendId: String
}

// MARK: - API 响应模型

/// 用户搜索响应
struct UserSearchResponse: Codable {
    let success: Bool
    let data: UserInfo?
    let error: String?
}

/// 好友列表响应
struct FriendListResponse: Codable {
    let success: Bool
    let data: FriendListData?
    let error: String?
}

/// 好友列表数据
struct FriendListData: Codable {
    let accepted: [Friendship]
    let pending: [Friendship]
    let rejected: [Friendship]
}

/// 好友操作响应
struct FriendOperationResponse: Codable {
    let success: Bool
    let message: String
    let data: FriendOperationData?
    let error: String?
}

/// 好友操作数据
struct FriendOperationData: Codable {
    let id: String
    let userId: String
    let friendId: String
    let status: FriendshipStatus
    let createdAt: Date
    let updatedAt: Date
}

// MARK: - 好友关系视图状态

/// 用于UI显示的好友关系状态
enum FriendRelationshipState: Equatable {
    case notFriend          // 不是好友，可以添加
    case pendingSent        // 已发送请求，等待对方确认
    case pendingReceived    // 收到请求，等待我确认
    case friend             // 已是好友
    case rejected           // 被拒绝，可以重新添加

    /// 按钮文本
    var buttonTitle: String {
        switch self {
        case .notFriend:
            return "加好友"
        case .pendingSent:
            return "已请求"
        case .pendingReceived:
            return "待确认"
        case .friend:
            return "查看资料"
        case .rejected:
            return "重新添加"
        }
    }

    /// 按钮是否可用
    var isButtonEnabled: Bool {
        switch self {
        case .notFriend, .pendingReceived, .friend, .rejected:
            return true
        case .pendingSent:
            return false
        }
    }

    /// 状态描述
    var statusDescription: String {
        switch self {
        case .notFriend:
            return "可以添加为好友"
        case .pendingSent:
            return "好友请求已发送"
        case .pendingReceived:
            return "收到好友请求"
        case .friend:
            return "已是好友"
        case .rejected:
            return "好友请求被拒绝"
        }
    }
}

// MARK: - 好友错误类型

/// 好友系统错误枚举
enum FriendError: Error, LocalizedError {
    case userNotFound
    case friendshipAlreadyExists
    case friendshipNotFound
    case cannotAddSelf
    case networkError
    case serverError
    case invalidResponse
    case unknown(String)

    var errorDescription: String? {
        switch self {
        case .userNotFound:
            return "用户不存在"
        case .friendshipAlreadyExists:
            return "好友关系已存在"
        case .friendshipNotFound:
            return "好友关系不存在"
        case .cannotAddSelf:
            return "不能添加自己为好友"
        case .networkError:
            return "网络连接失败，请检查网络设置"
        case .serverError:
            return "服务器错误，请稍后重试"
        case .invalidResponse:
            return "服务器响应格式错误"
        case .unknown(let message):
            return message
        }
    }
}
