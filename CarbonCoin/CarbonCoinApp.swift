//
//  CarbonCoinApp.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/8/15.
//
import SwiftUI
import TipKit

@main
struct CarbonCoinApp: App {
    init() {
        // 清除默认 Window 背景
        UIWindow.appearance().backgroundColor = .clear

        // 设置 NavigationBar 背景透明
        let appearance = UINavigationBarAppearance()
        appearance.configureWithTransparentBackground()
        UINavigationBar.appearance().standardAppearance = appearance
        UINavigationBar.appearance().scrollEdgeAppearance = appearance

        // 设置 ScrollView 背景透明
        UITableView.appearance().backgroundColor = .clear
        UICollectionView.appearance().backgroundColor = .clear

        // 配置 TipKit
        try? Tips.configure([
            .displayFrequency(.immediate),
            .datastoreLocation(.applicationDefault)
        ])
    }
    
    @StateObject private var petViewModel = CarbonPetViewModel()
    @StateObject private var healthManager = HealthManager()
    @StateObject private var appSettings = AppSettings()
    @StateObject private var cardStore = CardStore()

    var body: some Scene {
        WindowGroup {
            ContentView()
                .preferredColorScheme(.dark)  // 强制使用 Dark Mode
                .environmentObject(petViewModel)
                .environmentObject(healthManager)
                .environmentObject(appSettings)
                .environmentObject(cardStore)
            
                .onAppear {
                    // 立即请求必要权限
                    Task {
                        // 请求健康权限
                        try? await healthManager.requestAuthorization(for: [.steps, .calories])
                        print("✅ 健康权限请求完成")
                    }

                    // 启动后延迟2秒进行缓存清理与预加载（不影响启动速度）
                    DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
                        // 清理过期缓存
                        CacheManager.shared.cleanupExpired()

                        // 预加载最常用的周/月数据（步数/卡路里为主），避免阻塞UI
                        Task {
                            let preloadCombos: [(HealthDataType, TimePeriod)] = [
                                (.steps, .week), (.steps, .month),
                                (.calories, .week), (.calories, .month)
                            ]
                            for (type, period) in preloadCombos {
                                // 若已有未过期缓存，则跳过
                                let cache = CacheManager.shared.read(type: type, period: period)
                                if cache.hit && !cache.expired { continue }
                                // 拉取并缓存
                                _ = try? await healthManager.fetchHealthData(type: type, period: period)
                            }
                        }
                    }
                }
        }
        .modelContainer(for: CarbonCoin.self)
    }
}
